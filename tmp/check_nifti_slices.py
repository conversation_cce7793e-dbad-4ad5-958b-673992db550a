#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the number of slices in a NIfTI file
"""

import nibabel as nib
import numpy as np
import os
import sys

def check_nifti_slices(file_path):
    """
    Check the number of slices and dimensions of a NIfTI file
    
    Args:
        file_path (str): Path to the NIfTI file
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在")
            return
        
        # Load the NIfTI file
        print(f"正在分析文件: {file_path}")
        nii_img = nib.load(file_path)
        
        # Get the image data
        img_data = nii_img.get_fdata()
        
        # Get the shape of the image
        shape = img_data.shape
        print(f"图像维度: {shape}")
        
        # For medical images, typically the last dimension is the slice dimension
        if len(shape) == 3:
            # 3D image: (width, height, slices)
            width, height, slices = shape
            print(f"宽度: {width}")
            print(f"高度: {height}")
            print(f"切片数: {slices}")
        elif len(shape) == 4:
            # 4D image: (width, height, slices, time/channels)
            width, height, slices, fourth_dim = shape
            print(f"宽度: {width}")
            print(f"高度: {height}")
            print(f"切片数: {slices}")
            print(f"第四维度 (时间/通道): {fourth_dim}")
        else:
            print(f"图像有 {len(shape)} 个维度: {shape}")
        
        # Get voxel spacing information
        header = nii_img.header
        voxel_sizes = header.get_zooms()
        print(f"体素大小 (mm): {voxel_sizes}")
        
        # Get data type
        print(f"数据类型: {img_data.dtype}")
        
        # Get value range
        print(f"像素值范围: [{np.min(img_data):.2f}, {np.max(img_data):.2f}]")
        
        # Check for any NaN or infinite values
        nan_count = np.sum(np.isnan(img_data))
        inf_count = np.sum(np.isinf(img_data))
        if nan_count > 0:
            print(f"警告: 发现 {nan_count} 个 NaN 值")
        if inf_count > 0:
            print(f"警告: 发现 {inf_count} 个无穷值")
            
    except Exception as e:
        print(f"错误: 无法读取文件 {file_path}")
        print(f"错误信息: {str(e)}")

if __name__ == "__main__":
    # Default file path
    file_path = "tumor/Dataset017_Liver/imagesTr/liver_2.nii.gz"
    
    # Allow command line argument to override
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    
    check_nifti_slices(file_path)
